'use client';

import { motion } from 'framer-motion';
import {
  CheckBadgeIcon,
  GlobeAltIcon,
  TruckIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';

const WhyChooseUs = () => {
  const reasons = [
    {
      icon: GlobeAltIcon,
      title: '100% Natural',
      description: 'Pure, organic products sourced directly from healthy, well-cared-for cows.',
      color: 'text-green-600'
    },
    {
      icon: CheckBadgeIcon,
      title: 'Export Quality',
      description: 'International standards with rigorous quality control and certifications.',
      color: 'text-yellow-600'
    },
    {
      icon: TruckIcon,
      title: 'Eco-Friendly',
      description: 'Sustainable practices that protect the environment for future generations.',
      color: 'text-green-600'
    },
    {
      icon: ShieldCheckIcon,
      title: 'Fair Trade',
      description: 'Ethical sourcing that supports local farmers and rural communities.',
      color: 'text-amber-600'
    },
    {
      icon: GlobeAltIcon,
      title: 'Global Shipping',
      description: 'Reliable worldwide delivery with proper packaging and handling.',
      color: 'text-yellow-600'
    },
    {
      icon: CheckBadgeIcon,
      title: 'Quality Assured',
      description: 'Comprehensive testing and quality assurance for every batch.',
      color: 'text-green-600'
    }
  ];

  return (
    <section className="section-padding bg-gradient-to-br from-primary-50 to-accent-50">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-heading font-bold text-green-600 mb-4">
            Why Choose Gauveda Global?
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            We combine traditional wisdom with modern quality standards to deliver 
            products that exceed expectations and support sustainable living.
          </p>
        </motion.div>

        {/* Reasons Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {reasons.map((reason, index) => (
            <motion.div
              key={reason.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white p-8 rounded-lg card-shadow text-center group hover:scale-105 transition-transform duration-300"
            >
              <div className="mb-6">
                <div className="w-16 h-16 mx-auto bg-gradient-to-br from-primary-100 to-accent-100 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <reason.icon className={`w-8 h-8 ${reason.color}`} />
                </div>
              </div>
              
              <h3 className="text-xl font-heading font-semibold text-green-600 mb-4">
                {reason.title}
              </h3>
              
              <p className="text-gray-600 leading-relaxed">
                {reason.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 bg-white rounded-lg p-8 card-shadow"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-heading font-bold text-green-600 mb-2">500+</div>
              <div className="text-gray-600">Happy Customers</div>
            </div>
            <div>
              <div className="text-3xl font-heading font-bold text-yellow-500 mb-2">15+</div>
              <div className="text-gray-600">Countries Served</div>
            </div>
            <div>
              <div className="text-3xl font-heading font-bold text-amber-600 mb-2">1000+</div>
              <div className="text-gray-600">Tons Exported</div>
            </div>
            <div>
              <div className="text-3xl font-heading font-bold text-green-600 mb-2">99%</div>
              <div className="text-gray-600">Quality Rating</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
