'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { GlobeAltIcon, HeartIcon } from '@heroicons/react/24/outline';

const AboutPreview = () => {
  const features = [
    {
      icon: GlobeAltIcon,
      title: 'Sustainable',
      description: 'Eco-friendly products that support sustainable farming practices'
    },
    {
      icon: HeartIcon,
      title: 'Global Reach',
      description: 'Exporting premium quality products to markets worldwide'
    },
    {
      icon: HeartIcon,
      title: 'Ethical',
      description: 'Fair trade practices supporting local farmers and communities'
    }
  ];

  return (
    <section className="section-padding bg-cream">
      <div className="container-custom">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-heading font-bold text-green-600 mb-6">
              Gauveda Global
            </h2>
            <p className="text-lg text-gray-700 mb-6 leading-relaxed">
              Rooted in ancient Vedic traditions, we bring you premium cow-based products 
              that embody the essence of sustainable living. Our mission is to bridge the 
              gap between traditional wisdom and modern sustainability needs.
            </p>
            <p className="text-lg text-gray-700 mb-8 leading-relaxed">
              From organic cow dung manure that enriches soil naturally to eco-friendly 
              fuel logs that provide clean energy, every product reflects our commitment 
              to environmental stewardship and quality excellence.
            </p>
            
            <Link
              href="/about"
              className="btn-primary inline-block"
            >
              Learn More About Us
            </Link>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="grid gap-6"
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white p-6 rounded-lg card-shadow"
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <feature.icon className="w-6 h-6 text-green-600" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-heading font-semibold text-green-600 mb-2">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default AboutPreview;
