"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AboutPreview.tsx":
/*!*****************************************!*\
  !*** ./src/components/AboutPreview.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon,HeartIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon,HeartIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst AboutPreview = ()=>{\n    const features = [\n        {\n            icon: _barrel_optimize_names_GlobeAltIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: 'Sustainable',\n            description: 'Eco-friendly products that support sustainable farming practices'\n        },\n        {\n            icon: _barrel_optimize_names_GlobeAltIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: 'Global Reach',\n            description: 'Exporting premium quality products to markets worldwide'\n        },\n        {\n            icon: _barrel_optimize_names_GlobeAltIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: 'Ethical',\n            description: 'Fair trade practices supporting local farmers and communities'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-cream\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: -30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-heading font-bold text-primary-500 mb-6\",\n                                children: \"Gauveda Global\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-700 mb-6 leading-relaxed\",\n                                children: \"Rooted in ancient Vedic traditions, we bring you premium cow-based products that embody the essence of sustainable living. Our mission is to bridge the gap between traditional wisdom and modern sustainability needs.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-700 mb-8 leading-relaxed\",\n                                children: \"From organic cow dung manure that enriches soil naturally to eco-friendly fuel logs that provide clean energy, every product reflects our commitment to environmental stewardship and quality excellence.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/about\",\n                                className: \"btn-primary inline-block\",\n                                children: \"Learn More About Us\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"grid gap-6\",\n                        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"bg-white p-6 rounded-lg card-shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-6 h-6 text-primary-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-heading font-semibold text-primary-500 mb-2\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, feature.title, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AboutPreview;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AboutPreview);\nvar _c;\n$RefreshReg$(_c, \"AboutPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0Fib3V0UHJldmlldy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFdUM7QUFDVjtBQUN5QztBQUV0RSxNQUFNSSxlQUFlO0lBQ25CLE1BQU1DLFdBQVc7UUFDZjtZQUNFQyxNQUFNSixnSEFBWUE7WUFDbEJLLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0E7WUFDRUYsTUFBTUgsZ0hBQVNBO1lBQ2ZJLE9BQU87WUFDUEMsYUFBYTtRQUNmO1FBQ0E7WUFDRUYsTUFBTUgsZ0hBQVNBO1lBQ2ZJLE9BQU87WUFDUEMsYUFBYTtRQUNmO0tBQ0Q7SUFFRCxxQkFDRSw4REFBQ0M7UUFBUUMsV0FBVTtrQkFDakIsNEVBQUNDO1lBQUlELFdBQVU7c0JBQ2IsNEVBQUNDO2dCQUFJRCxXQUFVOztrQ0FFYiw4REFBQ1YsaURBQU1BLENBQUNXLEdBQUc7d0JBQ1RDLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUdDLEdBQUcsQ0FBQzt3QkFBRzt3QkFDOUJDLGFBQWE7NEJBQUVGLFNBQVM7NEJBQUdDLEdBQUc7d0JBQUU7d0JBQ2hDRSxZQUFZOzRCQUFFQyxVQUFVO3dCQUFJO3dCQUM1QkMsVUFBVTs0QkFBRUMsTUFBTTt3QkFBSzs7MENBRXZCLDhEQUFDQztnQ0FBR1YsV0FBVTswQ0FBb0U7Ozs7OzswQ0FHbEYsOERBQUNXO2dDQUFFWCxXQUFVOzBDQUE2Qzs7Ozs7OzBDQUsxRCw4REFBQ1c7Z0NBQUVYLFdBQVU7MENBQTZDOzs7Ozs7MENBTTFELDhEQUFDVCxrREFBSUE7Z0NBQ0hxQixNQUFLO2dDQUNMWixXQUFVOzBDQUNYOzs7Ozs7Ozs7Ozs7a0NBTUgsOERBQUNWLGlEQUFNQSxDQUFDVyxHQUFHO3dCQUNUQyxTQUFTOzRCQUFFQyxTQUFTOzRCQUFHQyxHQUFHO3dCQUFHO3dCQUM3QkMsYUFBYTs0QkFBRUYsU0FBUzs0QkFBR0MsR0FBRzt3QkFBRTt3QkFDaENFLFlBQVk7NEJBQUVDLFVBQVU7NEJBQUtNLE9BQU87d0JBQUk7d0JBQ3hDTCxVQUFVOzRCQUFFQyxNQUFNO3dCQUFLO3dCQUN2QlQsV0FBVTtrQ0FFVEwsU0FBU21CLEdBQUcsQ0FBQyxDQUFDQyxTQUFTQyxzQkFDdEIsOERBQUMxQixpREFBTUEsQ0FBQ1csR0FBRztnQ0FFVEMsU0FBUztvQ0FBRUMsU0FBUztvQ0FBR2MsR0FBRztnQ0FBRztnQ0FDN0JaLGFBQWE7b0NBQUVGLFNBQVM7b0NBQUdjLEdBQUc7Z0NBQUU7Z0NBQ2hDWCxZQUFZO29DQUFFQyxVQUFVO29DQUFLTSxPQUFPRyxRQUFRO2dDQUFJO2dDQUNoRFIsVUFBVTtvQ0FBRUMsTUFBTTtnQ0FBSztnQ0FDdkJULFdBQVU7MENBRVYsNEVBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQ0M7NENBQUlELFdBQVU7c0RBQ2IsNEVBQUNDO2dEQUFJRCxXQUFVOzBEQUNiLDRFQUFDZSxRQUFRbkIsSUFBSTtvREFBQ0ksV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztzREFHNUIsOERBQUNDOzs4REFDQyw4REFBQ2lCO29EQUFHbEIsV0FBVTs4REFDWGUsUUFBUWxCLEtBQUs7Ozs7Ozs4REFFaEIsOERBQUNjO29EQUFFWCxXQUFVOzhEQUNWZSxRQUFRakIsV0FBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQWxCckJpQixRQUFRbEIsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUE2QmxDO0tBM0ZNSDtBQTZGTixpRUFBZUEsWUFBWUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXHhhbXBwXFxodGRvY3NcXGdhdWdsb2JhbFxcZ2F1dmVkYS13ZWJzaXRlXFxzcmNcXGNvbXBvbmVudHNcXEFib3V0UHJldmlldy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBtb3Rpb24gfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XG5pbXBvcnQgeyBHbG9iZUFsdEljb24sIEhlYXJ0SWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5cbmNvbnN0IEFib3V0UHJldmlldyA9ICgpID0+IHtcbiAgY29uc3QgZmVhdHVyZXMgPSBbXG4gICAge1xuICAgICAgaWNvbjogR2xvYmVBbHRJY29uLFxuICAgICAgdGl0bGU6ICdTdXN0YWluYWJsZScsXG4gICAgICBkZXNjcmlwdGlvbjogJ0Vjby1mcmllbmRseSBwcm9kdWN0cyB0aGF0IHN1cHBvcnQgc3VzdGFpbmFibGUgZmFybWluZyBwcmFjdGljZXMnXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiBIZWFydEljb24sXG4gICAgICB0aXRsZTogJ0dsb2JhbCBSZWFjaCcsXG4gICAgICBkZXNjcmlwdGlvbjogJ0V4cG9ydGluZyBwcmVtaXVtIHF1YWxpdHkgcHJvZHVjdHMgdG8gbWFya2V0cyB3b3JsZHdpZGUnXG4gICAgfSxcbiAgICB7XG4gICAgICBpY29uOiBIZWFydEljb24sXG4gICAgICB0aXRsZTogJ0V0aGljYWwnLFxuICAgICAgZGVzY3JpcHRpb246ICdGYWlyIHRyYWRlIHByYWN0aWNlcyBzdXBwb3J0aW5nIGxvY2FsIGZhcm1lcnMgYW5kIGNvbW11bml0aWVzJ1xuICAgIH1cbiAgXTtcblxuICByZXR1cm4gKFxuICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInNlY3Rpb24tcGFkZGluZyBiZy1jcmVhbVwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXItY3VzdG9tXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBsZzpncmlkLWNvbHMtMiBnYXAtMTIgaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgey8qIENvbnRlbnQgKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogLTMwIH19XG4gICAgICAgICAgICB3aGlsZUluVmlldz17eyBvcGFjaXR5OiAxLCB4OiAwIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjggfX1cbiAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWQ6dGV4dC00eGwgZm9udC1oZWFkaW5nIGZvbnQtYm9sZCB0ZXh0LXByaW1hcnktNTAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgR2F1dmVkYSBHbG9iYWxcbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS03MDAgbWItNiBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgUm9vdGVkIGluIGFuY2llbnQgVmVkaWMgdHJhZGl0aW9ucywgd2UgYnJpbmcgeW91IHByZW1pdW0gY293LWJhc2VkIHByb2R1Y3RzIFxuICAgICAgICAgICAgICB0aGF0IGVtYm9keSB0aGUgZXNzZW5jZSBvZiBzdXN0YWluYWJsZSBsaXZpbmcuIE91ciBtaXNzaW9uIGlzIHRvIGJyaWRnZSB0aGUgXG4gICAgICAgICAgICAgIGdhcCBiZXR3ZWVuIHRyYWRpdGlvbmFsIHdpc2RvbSBhbmQgbW9kZXJuIHN1c3RhaW5hYmlsaXR5IG5lZWRzLlxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWdyYXktNzAwIG1iLTggbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgIEZyb20gb3JnYW5pYyBjb3cgZHVuZyBtYW51cmUgdGhhdCBlbnJpY2hlcyBzb2lsIG5hdHVyYWxseSB0byBlY28tZnJpZW5kbHkgXG4gICAgICAgICAgICAgIGZ1ZWwgbG9ncyB0aGF0IHByb3ZpZGUgY2xlYW4gZW5lcmd5LCBldmVyeSBwcm9kdWN0IHJlZmxlY3RzIG91ciBjb21taXRtZW50IFxuICAgICAgICAgICAgICB0byBlbnZpcm9ubWVudGFsIHN0ZXdhcmRzaGlwIGFuZCBxdWFsaXR5IGV4Y2VsbGVuY2UuXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGhyZWY9XCIvYWJvdXRcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4tcHJpbWFyeSBpbmxpbmUtYmxvY2tcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBMZWFybiBNb3JlIEFib3V0IFVzXG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuXG4gICAgICAgICAgey8qIEZlYXR1cmVzIEdyaWQgKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeDogMzAgfX1cbiAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHg6IDAgfX1cbiAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuOCwgZGVsYXk6IDAuMiB9fVxuICAgICAgICAgICAgdmlld3BvcnQ9e3sgb25jZTogdHJ1ZSB9fVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNlwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2ZlYXR1cmVzLm1hcCgoZmVhdHVyZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBrZXk9e2ZlYXR1cmUudGl0bGV9XG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICAgIHdoaWxlSW5WaWV3PXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjYsIGRlbGF5OiBpbmRleCAqIDAuMSB9fVxuICAgICAgICAgICAgICAgIHZpZXdwb3J0PXt7IG9uY2U6IHRydWUgfX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSBwLTYgcm91bmRlZC1sZyBjYXJkLXNoYWRvd1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgYmctcHJpbWFyeS0xMDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxmZWF0dXJlLmljb24gY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LXByaW1hcnktNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtaGVhZGluZyBmb250LXNlbWlib2xkIHRleHQtcHJpbWFyeS01MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtmZWF0dXJlLnRpdGxlfVxuICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2ZlYXR1cmUuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9zZWN0aW9uPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQWJvdXRQcmV2aWV3O1xuIl0sIm5hbWVzIjpbIm1vdGlvbiIsIkxpbmsiLCJHbG9iZUFsdEljb24iLCJIZWFydEljb24iLCJBYm91dFByZXZpZXciLCJmZWF0dXJlcyIsImljb24iLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwic2VjdGlvbiIsImNsYXNzTmFtZSIsImRpdiIsImluaXRpYWwiLCJvcGFjaXR5IiwieCIsIndoaWxlSW5WaWV3IiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwidmlld3BvcnQiLCJvbmNlIiwiaDIiLCJwIiwiaHJlZiIsImRlbGF5IiwibWFwIiwiZmVhdHVyZSIsImluZGV4IiwieSIsImgzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AboutPreview.tsx\n"));

/***/ })

});