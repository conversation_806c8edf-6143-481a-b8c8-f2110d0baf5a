"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/WhyChooseUs.tsx":
/*!****************************************!*\
  !*** ./src/components/WhyChooseUs.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckBadgeIcon_GlobeAltIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=CheckBadgeIcon,GlobeAltIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckBadgeIcon_GlobeAltIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckBadgeIcon,GlobeAltIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckBadgeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckBadgeIcon_GlobeAltIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckBadgeIcon,GlobeAltIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TruckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckBadgeIcon_GlobeAltIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckBadgeIcon,GlobeAltIcon,ShieldCheckIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst WhyChooseUs = ()=>{\n    const reasons = [\n        {\n            icon: _barrel_optimize_names_CheckBadgeIcon_GlobeAltIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            title: '100% Natural',\n            description: 'Pure, organic products sourced directly from healthy, well-cared-for cows.',\n            color: 'text-green-600'\n        },\n        {\n            icon: _barrel_optimize_names_CheckBadgeIcon_GlobeAltIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: 'Export Quality',\n            description: 'International standards with rigorous quality control and certifications.',\n            color: 'text-yellow-600'\n        },\n        {\n            icon: _barrel_optimize_names_CheckBadgeIcon_GlobeAltIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: 'Eco-Friendly',\n            description: 'Sustainable practices that protect the environment for future generations.',\n            color: 'text-green-600'\n        },\n        {\n            icon: _barrel_optimize_names_CheckBadgeIcon_GlobeAltIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: 'Fair Trade',\n            description: 'Ethical sourcing that supports local farmers and rural communities.',\n            color: 'text-amber-600'\n        },\n        {\n            icon: _barrel_optimize_names_CheckBadgeIcon_GlobeAltIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            title: 'Global Shipping',\n            description: 'Reliable worldwide delivery with proper packaging and handling.',\n            color: 'text-yellow-600'\n        },\n        {\n            icon: _barrel_optimize_names_CheckBadgeIcon_GlobeAltIcon_ShieldCheckIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: 'Quality Assured',\n            description: 'Comprehensive testing and quality assurance for every batch.',\n            color: 'text-green-600'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-gradient-to-br from-primary-50 to-accent-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-heading font-bold text-green-600 mb-4\",\n                            children: \"Why Choose Gauveda Global?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: \"We combine traditional wisdom with modern quality standards to deliver products that exceed expectations and support sustainable living.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: reasons.map((reason, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"bg-white p-8 rounded-lg card-shadow text-center group hover:scale-105 transition-transform duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 mx-auto bg-gradient-to-br from-primary-100 to-accent-100 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(reason.icon, {\n                                            className: \"w-8 h-8 \".concat(reason.color)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-heading font-semibold text-green-600 mb-4\",\n                                    children: reason.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 leading-relaxed\",\n                                    children: reason.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, reason.title, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-16 bg-white rounded-lg p-8 card-shadow\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-heading font-bold text-green-600 mb-2\",\n                                        children: \"500+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Happy Customers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-heading font-bold text-yellow-500 mb-2\",\n                                        children: \"15+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Countries Served\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-heading font-bold text-amber-600 mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Tons Exported\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-heading font-bold text-green-600 mb-2\",\n                                        children: \"99%\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Quality Rating\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhyChooseUs.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n_c = WhyChooseUs;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhyChooseUs);\nvar _c;\n$RefreshReg$(_c, \"WhyChooseUs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WhyChooseUs.tsx\n"));

/***/ })

});