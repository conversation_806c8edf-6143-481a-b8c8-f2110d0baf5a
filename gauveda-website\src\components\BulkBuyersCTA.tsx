'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  TruckIcon, 
  DocumentArrowDownIcon, 
  ChatBubbleLeftRightIcon 
} from '@heroicons/react/24/outline';

const BulkBuyersCTA = () => {
  const features = [
    {
      icon: TruckIcon,
      title: 'Global Shipping',
      description: 'Worldwide delivery with proper packaging'
    },
    {
      icon: DocumentArrowDownIcon,
      title: 'Product Catalog',
      description: 'Downloadable detailed specifications'
    },
    {
      icon: ChatBubbleLeftRightIcon,
      title: 'Custom Solutions',
      description: 'Tailored packages for your needs'
    }
  ];

  return (
    <section className="section-padding bg-green-600 text-white">
      <div className="container-custom">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-heading font-bold mb-6">
              Ready for Bulk Orders?
            </h2>
            <p className="text-xl mb-8 text-green-100">
              Partner with us for large-scale supply of premium cow-based products. 
              We offer competitive pricing, reliable delivery, and exceptional quality 
              for businesses worldwide.
            </p>
            
            <div className="space-y-4 mb-8">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-accent-400 rounded-full"></div>
                <span>Minimum Order Quantity: 1 Ton</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-accent-400 rounded-full"></div>
                <span>Export Documentation Included</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-accent-400 rounded-full"></div>
                <span>Flexible Payment Terms</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/bulk-buyers"
                className="btn-accent"
              >
                Get Bulk Quote
              </Link>
              <Link
                href="/contact"
                className="btn-outline border-white text-white hover:bg-white hover:text-green-600"
              >
                Contact Sales Team
              </Link>
            </div>
          </motion.div>

          {/* Features */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white bg-opacity-10 backdrop-blur-sm p-6 rounded-lg"
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                      <feature.icon className="w-6 h-6 text-green-900" />
                    </div>
                  </div>
                  <div>
                    <h3 className="text-xl font-heading font-semibold mb-2">
                      {feature.title}
                    </h3>
                    <p className="text-green-100">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default BulkBuyersCTA;
