/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 22a10 10 0 1 1 10-10", key: "130bv5" }],
  ["path", { d: "M22 22 12 12", key: "131aw7" }],
  ["path", { d: "M22 16v6h-6", key: "1gvm70" }]
];
const CircleArrowOutDownRight = createLucideIcon("circle-arrow-out-down-right", __iconNode);

export { __iconNode, CircleArrowOutDownRight as default };
//# sourceMappingURL=circle-arrow-out-down-right.js.map
