'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';

const ProductHighlights = () => {
  const products = [
    {
      id: 1,
      name: 'Cow Dung Manure',
      description: 'Premium organic fertilizer rich in nutrients for sustainable farming and gardening.',
      image: '/image.png',
      benefits: ['100% Organic', 'Nutrient Rich', 'Soil Health'],
      category: 'Fertilizer'
    },
    {
      id: 2,
      name: 'Cow Dung Cakes',
      description: 'Traditional fuel cakes for eco-friendly cooking and heating solutions.',
      image: '/image.png',
      benefits: ['Eco-Friendly', 'Clean Burning', 'Traditional'],
      category: 'Fuel'
    },
    {
      id: 3,
      name: 'Cow Dung Logs',
      description: 'Compressed fuel logs providing efficient and sustainable energy source.',
      image: '/image.png',
      benefits: ['High Efficiency', 'Long Burning', 'Smokeless'],
      category: 'Fuel'
    }
  ];

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-heading font-bold text-green-600 mb-4">
            Our Premium Products
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our range of high-quality cow-based products, each crafted with 
            care to meet the highest standards of sustainability and effectiveness.
          </p>
        </motion.div>

        {/* Products Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-lg card-shadow overflow-hidden group hover:scale-105 transition-transform duration-300"
            >
              {/* Product Image */}
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {product.category}
                  </span>
                </div>
              </div>

              {/* Product Content */}
              <div className="p-6">
                <h3 className="text-xl font-heading font-semibold text-green-600 mb-3">
                  {product.name}
                </h3>
                <p className="text-gray-600 mb-4">
                  {product.description}
                </p>

                {/* Benefits */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {product.benefits.map((benefit) => (
                    <span
                      key={benefit}
                      className="bg-accent-100 text-accent-700 px-2 py-1 rounded text-sm"
                    >
                      {benefit}
                    </span>
                  ))}
                </div>

                <button className="btn-outline w-full">
                  Learn More
                </button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* View All Products CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link
            href="/products"
            className="btn-primary text-lg px-8 py-4"
          >
            View All Products
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default ProductHighlights;
