"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/AboutPreview.tsx":
/*!*****************************************!*\
  !*** ./src/components/AboutPreview.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon,HeartIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon,HeartIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst AboutPreview = ()=>{\n    const features = [\n        {\n            icon: _barrel_optimize_names_GlobeAltIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: 'Sustainable',\n            description: 'Eco-friendly products that support sustainable farming practices'\n        },\n        {\n            icon: _barrel_optimize_names_GlobeAltIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: 'Global Reach',\n            description: 'Exporting premium quality products to markets worldwide'\n        },\n        {\n            icon: _barrel_optimize_names_GlobeAltIcon_HeartIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: 'Ethical',\n            description: 'Fair trade practices supporting local farmers and communities'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-cream\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: -30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-heading font-bold text-green-600 mb-6\",\n                                children: \"Gauveda Global\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-700 mb-6 leading-relaxed\",\n                                children: \"Rooted in ancient Vedic traditions, we bring you premium cow-based products that embody the essence of sustainable living. Our mission is to bridge the gap between traditional wisdom and modern sustainability needs.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-700 mb-8 leading-relaxed\",\n                                children: \"From organic cow dung manure that enriches soil naturally to eco-friendly fuel logs that provide clean energy, every product reflects our commitment to environmental stewardship and quality excellence.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/about\",\n                                className: \"btn-primary inline-block\",\n                                children: \"Learn More About Us\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"grid gap-6\",\n                        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"bg-white p-6 rounded-lg card-shadow\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-6 h-6 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-heading font-semibold text-green-600 mb-2\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, feature.title, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\AboutPreview.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, undefined);\n};\n_c = AboutPreview;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AboutPreview);\nvar _c;\n$RefreshReg$(_c, \"AboutPreview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AboutPreview.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/BulkBuyersCTA.tsx":
/*!******************************************!*\
  !*** ./src/components/BulkBuyersCTA.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_DocumentArrowDownIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,DocumentArrowDownIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TruckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_DocumentArrowDownIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,DocumentArrowDownIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_DocumentArrowDownIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,DocumentArrowDownIcon,TruckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst BulkBuyersCTA = ()=>{\n    const features = [\n        {\n            icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_DocumentArrowDownIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            title: 'Global Shipping',\n            description: 'Worldwide delivery with proper packaging'\n        },\n        {\n            icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_DocumentArrowDownIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: 'Product Catalog',\n            description: 'Downloadable detailed specifications'\n        },\n        {\n            icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_DocumentArrowDownIcon_TruckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: 'Custom Solutions',\n            description: 'Tailored packages for your needs'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-green-600 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: -30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-heading font-bold mb-6\",\n                                children: \"Ready for Bulk Orders?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl mb-8 text-green-100\",\n                                children: \"Partner with us for large-scale supply of premium cow-based products. We offer competitive pricing, reliable delivery, and exceptional quality for businesses worldwide.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-accent-400 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Minimum Order Quantity: 1 Ton\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-accent-400 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export Documentation Included\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-accent-400 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Flexible Payment Terms\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/bulk-buyers\",\n                                        className: \"btn-accent\",\n                                        children: \"Get Bulk Quote\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/contact\",\n                                        className: \"btn-outline border-white text-white hover:bg-white hover:text-green-600\",\n                                        children: \"Contact Sales Team\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        className: \"space-y-6\",\n                        children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: index * 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                className: \"bg-white bg-opacity-10 backdrop-blur-sm p-6 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                    className: \"w-6 h-6 text-green-900\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-heading font-semibold mb-2\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-green-100\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, feature.title, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\BulkBuyersCTA.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n_c = BulkBuyersCTA;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BulkBuyersCTA);\nvar _c;\n$RefreshReg$(_c, \"BulkBuyersCTA\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/BulkBuyersCTA.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,GlobeAltIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapPinIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,GlobeAltIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhoneIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,GlobeAltIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=EnvelopeIcon,GlobeAltIcon,MapPinIcon,PhoneIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Footer = ()=>{\n    const quickLinks = [\n        {\n            name: 'Home',\n            href: '/'\n        },\n        {\n            name: 'About Us',\n            href: '/about'\n        },\n        {\n            name: 'Products',\n            href: '/products'\n        },\n        {\n            name: 'Bulk Buyers',\n            href: '/bulk-buyers'\n        },\n        {\n            name: 'Contact',\n            href: '/contact'\n        }\n    ];\n    const products = [\n        {\n            name: 'Cow Dung Manure',\n            href: '/products#manure'\n        },\n        {\n            name: 'Cow Dung Cakes',\n            href: '/products#cakes'\n        },\n        {\n            name: 'Cow Dung Logs',\n            href: '/products#logs'\n        },\n        {\n            name: 'Custom Products',\n            href: '/contact'\n        }\n    ];\n    const services = [\n        {\n            name: 'Bulk Supply',\n            href: '/bulk-buyers'\n        },\n        {\n            name: 'Export Services',\n            href: '/bulk-buyers'\n        },\n        {\n            name: 'Quality Assurance',\n            href: '/about'\n        },\n        {\n            name: 'Custom Packaging',\n            href: '/contact'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-primary-800 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"section-padding\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/\",\n                                        className: \"flex items-center space-x-2 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: \"/logo.png\",\n                                                alt: \"Gauveda Global\",\n                                                width: 40,\n                                                height: 40,\n                                                className: \"h-10 w-auto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-heading font-bold\",\n                                                        children: \"Gauveda Global\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 52,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-primary-200\",\n                                                        children: \"From Vedas to World\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 55,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-primary-200 mb-6 leading-relaxed\",\n                                        children: \"Premium cow-based products for sustainable living. Bridging ancient wisdom with modern sustainability needs.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"w-5 h-5 text-accent-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary-200\",\n                                                        children: \"Mumbai, Maharashtra, India\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 67,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"w-5 h-5 text-accent-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary-200\",\n                                                        children: \"+91 98765 43210\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-5 h-5 text-accent-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary-200\",\n                                                        children: \"<EMAIL>\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-accent-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-primary-200\",\n                                                        children: \"www.gauvedaglobal.com\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-heading font-semibold mb-6\",\n                                        children: \"Quick Links\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-primary-200 hover:text-accent-400 transition-colors duration-200\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, link.name, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-heading font-semibold mb-6\",\n                                        children: \"Our Products\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: product.href,\n                                                    className: \"text-primary-200 hover:text-accent-400 transition-colors duration-200\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, product.name, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-heading font-semibold mb-6\",\n                                        children: \"Services\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: services.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: service.href,\n                                                    className: \"text-primary-200 hover:text-accent-400 transition-colors duration-200\",\n                                                    children: service.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, service.name, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"font-semibold mb-3\",\n                                                children: \"Stay Updated\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"email\",\n                                                        placeholder: \"Your email\",\n                                                        className: \"flex-1 px-3 py-2 bg-primary-700 border border-primary-600 rounded-l-lg text-white placeholder-primary-300 focus:outline-none focus:border-accent-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"bg-yellow-500 hover:bg-accent-600 px-4 py-2 rounded-r-lg transition-colors duration-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_EnvelopeIcon_GlobeAltIcon_MapPinIcon_PhoneIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-900\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-primary-700 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-primary-300 text-sm\",\n                                children: \"\\xa9 2024 Gauveda Global. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-6 mt-4 md:mt-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/privacy\",\n                                        className: \"text-primary-300 hover:text-accent-400 text-sm transition-colors duration-200\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/terms\",\n                                        className: \"text-primary-300 hover:text-accent-400 text-sm transition-colors duration-200\",\n                                        children: \"Terms of Service\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/sitemap\",\n                                        className: \"text-primary-300 hover:text-accent-400 text-sm transition-colors duration-200\",\n                                        children: \"Sitemap\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Footer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Footer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/HeroSection.tsx":
/*!****************************************!*\
  !*** ./src/components/HeroSection.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst HeroSection = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: \"/heroback.png\",\n                        alt: \"Lush green fields\",\n                        fill: true,\n                        className: \"object-cover\",\n                        priority: true\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black bg-opacity-40\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 container-custom text-center text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                className: \"text-4xl md:text-6xl font-heading font-bold mb-6\",\n                                children: [\n                                    \"From the Land of\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-accent-400\",\n                                        children: \"Vedas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    ' ',\n                                    \"to the World\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                className: \"text-xl md:text-2xl mb-8 text-gray-200\",\n                                children: \"Premium Cow-Based Products for a Sustainable Future\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.6\n                                },\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/products\",\n                                        className: \"btn-accent text-lg px-8 py-4 hover:scale-105 transform transition-all duration-200\",\n                                        children: \"Explore Products\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/bulk-buyers\",\n                                        className: \"btn-outline text-lg px-8 py-4 border-white text-white hover:bg-white hover:text-green-600 hover:scale-105 transform transition-all duration-200\",\n                                        children: \"For Bulk Buyers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        animate: {\n                            y: [\n                                0,\n                                -10,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 3,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-20 left-10 hidden lg:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-green-600 bg-opacity-20 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl\",\n                                children: \"\\uD83C\\uDF3F\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        animate: {\n                            y: [\n                                0,\n                                -15,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 4,\n                            repeat: Infinity,\n                            ease: \"easeInOut\",\n                            delay: 1\n                        },\n                        className: \"absolute top-32 right-20 hidden lg:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-12 h-12 bg-yellow-500 bg-opacity-20 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl\",\n                                children: \"\\uD83C\\uDF3E\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        animate: {\n                            y: [\n                                0,\n                                -8,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 3.5,\n                            repeat: Infinity,\n                            ease: \"easeInOut\",\n                            delay: 0.5\n                        },\n                        className: \"absolute bottom-32 left-20 hidden lg:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-14 h-14 bg-amber-600 bg-opacity-20 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl\",\n                                children: \"☀️\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                animate: {\n                    y: [\n                        0,\n                        10,\n                        0\n                    ]\n                },\n                transition: {\n                    duration: 2,\n                    repeat: Infinity\n                },\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm mb-2\",\n                            children: \"Scroll Down\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-6 h-10 border-2 border-white rounded-full flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-1 h-3 bg-white rounded-full mt-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\HeroSection.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_c = HeroSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HeroSection);\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/HeroSection.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Navigation = ()=>{\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const navigation = [\n        {\n            name: 'Home',\n            href: '/'\n        },\n        {\n            name: 'About Us',\n            href: '/about'\n        },\n        {\n            name: 'Products',\n            href: '/products'\n        },\n        {\n            name: 'Bulk Buyers',\n            href: '/bulk-buyers'\n        },\n        {\n            name: 'Contact',\n            href: '/contact'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-lg sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"/logo.png\",\n                                    alt: \"Gauveda Global\",\n                                    width: 50,\n                                    height: 50,\n                                    className: \"h-12 w-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-heading font-bold text-green-600\",\n                                            children: \"Gauveda Global\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-amber-600\",\n                                            children: \"From Vedas to World\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: item.href,\n                                        className: \"text-green-700 hover:text-green-600 font-medium transition-colors duration-200\",\n                                        children: item.name\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/contact\",\n                                    className: \"btn-primary\",\n                                    children: \"Get Quote\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(!isOpen),\n                                className: \"text-green-600 hover:text-green-700\",\n                                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, undefined),\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden pb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-2\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: \"text-green-700 hover:text-green-600 font-medium py-2 transition-colors duration-200\",\n                                    onClick: ()=>setIsOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/contact\",\n                                className: \"btn-primary mt-4 text-center\",\n                                onClick: ()=>setIsOpen(false),\n                                children: \"Get Quote\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Navigation.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navigation, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c = Navigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Navigation.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ProductHighlights.tsx":
/*!**********************************************!*\
  !*** ./src/components/ProductHighlights.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst ProductHighlights = ()=>{\n    const products = [\n        {\n            id: 1,\n            name: 'Cow Dung Manure',\n            description: 'Premium organic fertilizer rich in nutrients for sustainable farming and gardening.',\n            image: '/image.png',\n            benefits: [\n                '100% Organic',\n                'Nutrient Rich',\n                'Soil Health'\n            ],\n            category: 'Fertilizer'\n        },\n        {\n            id: 2,\n            name: 'Cow Dung Cakes',\n            description: 'Traditional fuel cakes for eco-friendly cooking and heating solutions.',\n            image: '/image.png',\n            benefits: [\n                'Eco-Friendly',\n                'Clean Burning',\n                'Traditional'\n            ],\n            category: 'Fuel'\n        },\n        {\n            id: 3,\n            name: 'Cow Dung Logs',\n            description: 'Compressed fuel logs providing efficient and sustainable energy source.',\n            image: '/image.png',\n            benefits: [\n                'High Efficiency',\n                'Long Burning',\n                'Smokeless'\n            ],\n            category: 'Fuel'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-heading font-bold text-green-600 mb-4\",\n                            children: \"Our Premium Products\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Discover our range of high-quality cow-based products, each crafted with care to meet the highest standards of sustainability and effectiveness.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\",\n                    children: products.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"bg-white rounded-lg card-shadow overflow-hidden group hover:scale-105 transition-transform duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-48 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: product.image,\n                                            alt: product.name,\n                                            fill: true,\n                                            className: \"object-cover group-hover:scale-110 transition-transform duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                                children: product.category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-heading font-semibold text-green-600 mb-3\",\n                                            children: product.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: product.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-4\",\n                                            children: product.benefits.map((benefit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-accent-100 text-accent-700 px-2 py-1 rounded text-sm\",\n                                                    children: benefit\n                                                }, benefit, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn-outline w-full\",\n                                            children: \"Learn More\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, product.id, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/products\",\n                        className: \"btn-primary text-lg px-8 py-4\",\n                        children: \"View All Products\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\ProductHighlights.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ProductHighlights;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductHighlights);\nvar _c;\n$RefreshReg$(_c, \"ProductHighlights\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductHighlights.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Testimonials.tsx":
/*!*****************************************!*\
  !*** ./src/components/Testimonials.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=StarIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/StarIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Testimonials = ()=>{\n    const testimonials = [\n        {\n            id: 1,\n            name: 'Rajesh Kumar',\n            role: 'Organic Farmer',\n            location: 'Punjab, India',\n            content: 'The cow dung manure from Gauveda Global has transformed my farm. My crop yield increased by 40% and the soil health has improved dramatically. Highly recommended!',\n            rating: 5,\n            image: '/image.png'\n        },\n        {\n            id: 2,\n            name: 'Sarah Johnson',\n            role: 'Import Manager',\n            location: 'California, USA',\n            content: 'We have been importing cow dung products for 2 years now. The quality is consistently excellent and the team is very professional. Great partnership!',\n            rating: 5,\n            image: '/image.png'\n        },\n        {\n            id: 3,\n            name: 'Ahmed Al-Rashid',\n            role: 'Agricultural Consultant',\n            location: 'Dubai, UAE',\n            content: 'Gauveda Global provides the best organic fertilizers in the market. Their export documentation is always perfect and delivery is always on time.',\n            rating: 5,\n            image: '/image.png'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"section-padding bg-cream\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-custom\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-heading font-bold text-green-600 mb-4\",\n                            children: \"What Our Customers Say\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Don't just take our word for it. Here's what farmers, exporters, and businesses around the world say about our products and services.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            className: \"bg-white p-6 rounded-lg card-shadow\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        ...Array(testimonial.rating)\n                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_StarIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: \"w-5 h-5 text-yellow-500\"\n                                        }, i, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-6 italic leading-relaxed\",\n                                    children: [\n                                        '\"',\n                                        testimonial.content,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 font-semibold text-lg\",\n                                                children: testimonial.name.charAt(0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-green-600\",\n                                                    children: testimonial.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: testimonial.role\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: testimonial.location\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, testimonial.id, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.4\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    className: \"mt-16 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-8 rounded-lg card-shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-heading font-semibold text-green-600 mb-6\",\n                                children: \"Trusted by Businesses Worldwide\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-yellow-500 mb-2\",\n                                                children: \"4.9/5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Average Rating\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-green-600 mb-2\",\n                                                children: \"500+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Happy Customers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-amber-600 mb-2\",\n                                                children: \"98%\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Repeat Orders\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold text-yellow-500 mb-2\",\n                                                children: \"24/7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Support\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\Testimonials.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Testimonials;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Testimonials);\nvar _c;\n$RefreshReg$(_c, \"Testimonials\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Testimonials.tsx\n"));

/***/ })

});