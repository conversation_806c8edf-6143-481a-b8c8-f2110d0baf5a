@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Open+Sans:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  --background: #F9F6F1;
  --foreground: #16a34a;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Open Sans', sans-serif;
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Open Sans', sans-serif;
}

@layer base {
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-amber-600 hover:bg-amber-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200;
  }

  .btn-accent {
    @apply bg-yellow-500 hover:bg-yellow-600 text-green-900 font-semibold py-3 px-6 rounded-lg transition-colors duration-200;
  }

  .btn-outline {
    @apply border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200;
  }

  .section-padding {
    @apply py-16 px-4 sm:px-6 lg:px-8;
  }

  .container-custom {
    @apply max-w-7xl mx-auto;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-green-600 to-amber-600 bg-clip-text text-transparent;
  }

  .card-shadow {
    @apply shadow-lg hover:shadow-xl transition-shadow duration-300;
  }
}
