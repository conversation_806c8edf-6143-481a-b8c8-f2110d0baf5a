'use client';

import Link from 'next/link';
import Image from 'next/image';
import { 
  MapPinIcon, 
  PhoneIcon, 
  EnvelopeIcon,
  GlobeAltIcon 
} from '@heroicons/react/24/outline';

const Footer = () => {
  const quickLinks = [
    { name: 'Home', href: '/' },
    { name: 'About Us', href: '/about' },
    { name: 'Products', href: '/products' },
    { name: 'Bulk Buyers', href: '/bulk-buyers' },
    { name: 'Contact', href: '/contact' },
  ];

  const products = [
    { name: 'Cow Dung Manure', href: '/products#manure' },
    { name: 'Cow Dung Cakes', href: '/products#cakes' },
    { name: 'Cow Dung Logs', href: '/products#logs' },
    { name: 'Custom Products', href: '/contact' },
  ];

  const services = [
    { name: 'Bulk Supply', href: '/bulk-buyers' },
    { name: 'Export Services', href: '/bulk-buyers' },
    { name: 'Quality Assurance', href: '/about' },
    { name: 'Custom Packaging', href: '/contact' },
  ];

  return (
    <footer className="bg-primary-800 text-white">
      <div className="container-custom">
        {/* Main Footer Content */}
        <div className="section-padding">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="lg:col-span-1">
              <Link href="/" className="flex items-center space-x-2 mb-6">
                <Image
                  src="/logo.png"
                  alt="Gauveda Global"
                  width={40}
                  height={40}
                  className="h-10 w-auto"
                />
                <div>
                  <h3 className="text-xl font-heading font-bold">
                    Gauveda Global
                  </h3>
                  <p className="text-xs text-primary-200">From Vedas to World</p>
                </div>
              </Link>
              <p className="text-primary-200 mb-6 leading-relaxed">
                Premium cow-based products for sustainable living. 
                Bridging ancient wisdom with modern sustainability needs.
              </p>
              
              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <MapPinIcon className="w-5 h-5 text-accent-400" />
                  <span className="text-sm text-primary-200">
                    Mumbai, Maharashtra, India
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <PhoneIcon className="w-5 h-5 text-accent-400" />
                  <span className="text-sm text-primary-200">
                    +91 98765 43210
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <EnvelopeIcon className="w-5 h-5 text-accent-400" />
                  <span className="text-sm text-primary-200">
                    <EMAIL>
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <GlobeAltIcon className="w-5 h-5 text-accent-400" />
                  <span className="text-sm text-primary-200">
                    www.gauvedaglobal.com
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-heading font-semibold mb-6">
                Quick Links
              </h4>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-primary-200 hover:text-accent-400 transition-colors duration-200"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Products */}
            <div>
              <h4 className="text-lg font-heading font-semibold mb-6">
                Our Products
              </h4>
              <ul className="space-y-3">
                {products.map((product) => (
                  <li key={product.name}>
                    <Link
                      href={product.href}
                      className="text-primary-200 hover:text-accent-400 transition-colors duration-200"
                    >
                      {product.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Services */}
            <div>
              <h4 className="text-lg font-heading font-semibold mb-6">
                Services
              </h4>
              <ul className="space-y-3">
                {services.map((service) => (
                  <li key={service.name}>
                    <Link
                      href={service.href}
                      className="text-primary-200 hover:text-accent-400 transition-colors duration-200"
                    >
                      {service.name}
                    </Link>
                  </li>
                ))}
              </ul>
              
              {/* Newsletter Signup */}
              <div className="mt-8">
                <h5 className="font-semibold mb-3">Stay Updated</h5>
                <div className="flex">
                  <input
                    type="email"
                    placeholder="Your email"
                    className="flex-1 px-3 py-2 bg-primary-700 border border-primary-600 rounded-l-lg text-white placeholder-primary-300 focus:outline-none focus:border-accent-400"
                  />
                  <button className="bg-yellow-500 hover:bg-accent-600 px-4 py-2 rounded-r-lg transition-colors duration-200">
                    <EnvelopeIcon className="w-5 h-5 text-green-900" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="border-t border-primary-700 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-primary-300 text-sm">
              © 2024 Gauveda Global. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link
                href="/privacy"
                className="text-primary-300 hover:text-accent-400 text-sm transition-colors duration-200"
              >
                Privacy Policy
              </Link>
              <Link
                href="/terms"
                className="text-primary-300 hover:text-accent-400 text-sm transition-colors duration-200"
              >
                Terms of Service
              </Link>
              <Link
                href="/sitemap"
                className="text-primary-300 hover:text-accent-400 text-sm transition-colors duration-200"
              >
                Sitemap
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
