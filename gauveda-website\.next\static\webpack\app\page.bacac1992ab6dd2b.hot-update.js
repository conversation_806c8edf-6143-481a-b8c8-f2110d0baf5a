/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CAboutPreview.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CBulkBuyersCTA.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CProductHighlights.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CTestimonials.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CWhatsAppChat.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CWhyChooseUs.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CAboutPreview.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CBulkBuyersCTA.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CProductHighlights.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CTestimonials.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CWhatsAppChat.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CWhyChooseUs.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AboutPreview.tsx */ \"(app-pages-browser)/./src/components/AboutPreview.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/BulkBuyersCTA.tsx */ \"(app-pages-browser)/./src/components/BulkBuyersCTA.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer.tsx */ \"(app-pages-browser)/./src/components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/HeroSection.tsx */ \"(app-pages-browser)/./src/components/HeroSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(app-pages-browser)/./src/components/Navigation.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ProductHighlights.tsx */ \"(app-pages-browser)/./src/components/ProductHighlights.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Testimonials.tsx */ \"(app-pages-browser)/./src/components/Testimonials.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WhatsAppChat.tsx */ \"(app-pages-browser)/./src/components/WhatsAppChat.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/WhyChooseUs.tsx */ \"(app-pages-browser)/./src/components/WhyChooseUs.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CAboutPreview.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CBulkBuyersCTA.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CHeroSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CProductHighlights.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CTestimonials.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CWhatsAppChat.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Cxampp%5C%5Chtdocs%5C%5Cgauglobal%5C%5Cgauveda-website%5C%5Csrc%5C%5Ccomponents%5C%5CWhyChooseUs.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/WhatsAppChat.tsx":
/*!*****************************************!*\
  !*** ./src/components/WhatsAppChat.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst WhatsAppChat = ()=>{\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const whatsappNumber = '+919876543210'; // Replace with actual WhatsApp number\n    const quickMessages = [\n        'Hi! I want to know more about your cow dung products.',\n        'I need a bulk quote for cow dung manure.',\n        'What are your export procedures?',\n        'Can you provide product specifications?'\n    ];\n    const sendWhatsAppMessage = (message)=>{\n        const encodedMessage = encodeURIComponent(message);\n        const whatsappUrl = \"https://wa.me/\".concat(whatsappNumber, \"?text=\").concat(encodedMessage);\n        window.open(whatsappUrl, '_blank');\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 right-6 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsOpen(!isOpen),\n                    className: \"bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg transition-all duration-300 hover:scale-110\",\n                    \"aria-label\": \"WhatsApp Chat\",\n                    children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-6 h-6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-6 h-6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-24 right-6 z-50 w-80 bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-green-500 text-white p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold\",\n                                            children: \"Gauveda Global\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-100\",\n                                            children: \"Typically replies instantly\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-100 rounded-lg p-3 mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-700\",\n                                            children: \"\\uD83D\\uDC4B Hello! How can we help you today?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mb-3\",\n                                        children: \"Choose a quick message or start typing:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 mb-4\",\n                                children: quickMessages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>sendWhatsAppMessage(message),\n                                        className: \"w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg text-sm text-gray-700 transition-colors duration-200\",\n                                        children: message\n                                    }, index, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t pt-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>sendWhatsAppMessage('Hi! I have a question about your products.'),\n                                    className: \"w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-lg font-medium transition-colors duration-200\",\n                                    children: \"Start Chat on WhatsApp\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 px-4 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 text-center\",\n                            children: \"We'll respond as soon as possible\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40 bg-black bg-opacity-20\",\n                onClick: ()=>setIsOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\gauglobal\\\\gauveda-website\\\\src\\\\components\\\\WhatsAppChat.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(WhatsAppChat, \"+sus0Lb0ewKHdwiUhiTAJFoFyQ0=\");\n_c = WhatsAppChat;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhatsAppChat);\nvar _c;\n$RefreshReg$(_c, \"WhatsAppChat\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WhatsAppChat.tsx\n"));

/***/ })

});