/**
 * @license lucide-react v0.539.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 9v7", key: "ylp826" }],
  ["path", { d: "M14 6v10", key: "1jy4vg" }],
  ["circle", { cx: "17.5", cy: "12.5", r: "3.5", key: "1a9481" }],
  ["circle", { cx: "6.5", cy: "12.5", r: "3.5", key: "2jlv1r" }]
];
const CaseLower = createLucideIcon("case-lower", __iconNode);

export { __iconNode, CaseLower as default };
//# sourceMappingURL=case-lower.js.map
